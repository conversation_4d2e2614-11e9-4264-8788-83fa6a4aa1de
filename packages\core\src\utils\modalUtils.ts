/**
 * Modal 相关工具函数
 * 用于优化弹框体验，防止页面抖动
 */

/**
 * 检测浏览器是否支持 scrollbar-gutter
 */
export const supportsScrollbarGutter = (): boolean => {
  if (typeof window === 'undefined') return false
  
  return CSS.supports('scrollbar-gutter', 'stable')
}

/**
 * 获取当前滚动条宽度
 */
export const getScrollbarWidth = (): number => {
  if (typeof window === 'undefined') return 0
  
  // 创建一个临时元素来测量滚动条宽度
  const outer = document.createElement('div')
  outer.style.visibility = 'hidden'
  outer.style.overflow = 'scroll'
  outer.style.msOverflowStyle = 'scrollbar' // needed for WinJS apps
  document.body.appendChild(outer)

  const inner = document.createElement('div')
  outer.appendChild(inner)

  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth
  outer.parentNode?.removeChild(outer)

  return scrollbarWidth
}

/**
 * 检查页面是否有垂直滚动条
 */
export const hasVerticalScrollbar = (): boolean => {
  if (typeof window === 'undefined') return false
  
  return document.body.scrollHeight > window.innerHeight
}

/**
 * Modal 配置优化选项
 */
export interface ModalOptimizationConfig {
  /** 是否禁用 body 滚动锁定 */
  disableBodyScrollLock?: boolean
  /** 是否禁用滚动条补偿 */
  disableScrollbarCompensation?: boolean
  /** 自定义滚动条补偿值 */
  customScrollbarWidth?: number
}

/**
 * 获取优化后的 Modal 配置
 * 根据浏览器支持情况和用户配置返回最佳的 Modal 属性
 */
export const getOptimizedModalConfig = (config: ModalOptimizationConfig = {}) => {
  const {
    disableBodyScrollLock = false,
    disableScrollbarCompensation = false,
    customScrollbarWidth
  } = config

  // 如果浏览器支持 scrollbar-gutter，推荐禁用滚动条补偿
  const shouldDisableCompensation = supportsScrollbarGutter() || disableScrollbarCompensation

  return {
    // Ant Design Modal 的配置
    modalProps: {
      // 如果需要禁用 body 滚动锁定
      ...(disableBodyScrollLock && {
        getContainer: false, // 不渲染到 body
      }),
    },
    
    // 样式相关的建议
    styles: {
      // 如果需要自定义遮罩样式
      mask: shouldDisableCompensation ? {
        // 当禁用滚动条补偿时，可能需要调整遮罩位置
      } : {},
    },
    
    // 调试信息
    debug: {
      supportsScrollbarGutter: supportsScrollbarGutter(),
      scrollbarWidth: customScrollbarWidth ?? getScrollbarWidth(),
      hasScrollbar: hasVerticalScrollbar(),
      shouldDisableCompensation,
    }
  }
}

/**
 * 为 Modal 组件提供的 Hook
 * 返回优化后的配置和调试信息
 */
export const useModalOptimization = (config: ModalOptimizationConfig = {}) => {
  const optimizedConfig = getOptimizedModalConfig(config)
  
  return {
    ...optimizedConfig,
    // 便捷方法
    isOptimized: optimizedConfig.debug.supportsScrollbarGutter,
    needsManualFix: !optimizedConfig.debug.supportsScrollbarGutter && optimizedConfig.debug.hasScrollbar,
  }
}

/**
 * 全局 Modal 配置建议
 * 可以在 ConfigProvider 中使用
 */
export const getGlobalModalConfig = () => {
  return {
    modal: {
      // 如果浏览器支持 scrollbar-gutter，可以考虑禁用默认的滚动条补偿
      ...(supportsScrollbarGutter() && {
        // 注意：Ant Design 可能没有直接的 API 来禁用滚动条补偿
        // 这里主要是通过 CSS 来处理
      })
    }
  }
}

/**
 * 调试工具：在控制台输出当前页面的滚动条信息
 */
export const debugScrollbarInfo = () => {
  if (typeof window === 'undefined') {
    console.log('debugScrollbarInfo: 只能在浏览器环境中使用')
    return
  }

  const info = {
    supportsScrollbarGutter: supportsScrollbarGutter(),
    scrollbarWidth: getScrollbarWidth(),
    hasVerticalScrollbar: hasVerticalScrollbar(),
    viewportWidth: window.innerWidth,
    documentWidth: document.documentElement.scrollWidth,
    bodyWidth: document.body.scrollWidth,
  }

  console.group('📏 滚动条信息调试')
  console.table(info)
  console.log('💡 建议：')
  if (info.supportsScrollbarGutter) {
    console.log('✅ 浏览器支持 scrollbar-gutter，推荐使用 CSS 方案')
  } else {
    console.log('⚠️ 浏览器不支持 scrollbar-gutter，使用兼容方案')
  }
  
  if (!info.hasVerticalScrollbar) {
    console.log('ℹ️ 当前页面没有垂直滚动条，不会出现抖动问题')
  }
  console.groupEnd()

  return info
}
