/* stylelint-disable at-rule-no-unknown */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 默认值 */
  --nb-font-size: 10px;
  --adm-font-family: var(--font-family-miSansMedium380), 'Microsoft Yahei', sans-serif;
}

html {
  font-size: var(--nb-font-size, 10px);
  transition: font-size 0.2s ease-in-out;
  text-size-adjust: 100%; /* 禁用自动字体调整 */
  touch-action: manipulation;
}

body {
  /* 预留滚动条空间，防止弹框出现时页面抖动 */
  scrollbar-gutter: stable;
}

/* #app必须保留 */
#app {
  height: 100%;
}

/*========= 自定义antd-mobile组件样式 =========*/
@import url('./components/tabs.css');
@import url('./components/sidebar.css');
@import url('./components/dropdown.css');
@import url('./components/checkoutlist.css');
@import url('./components/form.css');
@import url('./components/checkbox.css');
@import url('./components/dialog.css');
@import url('./components//imageuploader.css');
@import url('./components/card.css');
@import url('./components/collapse.css');
@import url('./components/imageviewer.css');
@import url('./components/SwipeAction.css');
@import url('./components/button.css');
@import url('./components/navbar.css');

/*========= 自定义样式 =========*/

.max-container {
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
  box-sizing: border-box;
}

.scroll-container::-webkit-scrollbar {
  height: 0; /* 隐藏水平滚动条 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: transparent; /* 可选：设置滑块为透明，确保不会显示 */
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

.search-input .adm-input-clear {
  padding: 0.8rem;
}

.adm-badge .adm-badge-content {
  font-size: 1.2rem;
}

.adm-error-block .adm-error-block-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.adm-stepper-middle {
  border-left: 1px solid #f3f3f4 !important;
  border-right: 1px solid #f3f3f4 !important;
}

.adm-stepper-minus:disabled,
.adm-stepper-plus:disabled {
  background: #fff;
}

.adm-error-block .adm-error-block-description {
  margin-top: 0px !important;
}

.adm-error-block .adm-error-block-description-title {
  display: none;
}

.coupon-desc-content p {
  font-size: 14px;
  font-family: var(--font-family-miSansRegular330);
  line-height: 1.6;
  margin-bottom: 20px;
  color: #6e6e73;

  &:last-child {
    margin-bottom: 0;
  }
}

/* ============ 弹框抖动优化 ============ */

/* 为支持 scrollbar-gutter 的浏览器提供更好的体验 */
@supports (scrollbar-gutter: stable) {
  /* 当弹框打开时，禁用默认的 padding-right 调整 */
  body.adm-overflow-hidden {
    padding-right: 0 !important;
  }
}

/* 为不支持 scrollbar-gutter 的浏览器提供兼容方案 */
@supports not (scrollbar-gutter: stable) {
  /* 移动端通常不需要滚动条补偿，但为了保险起见 */
  body.adm-overflow-hidden {
    padding-right: 0 !important;
  }
}
