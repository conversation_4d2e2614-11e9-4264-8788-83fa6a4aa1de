# Modal 弹框抖动优化方案 - 完整解决方案

## 问题描述

Ant Design 的弹框组件（Modal、Drawer、Popconfirm 等）在显示时会：

1. 给 `<body>` 添加 `overflow: hidden` 来禁止滚动
2. 根据滚动条宽度添加 `padding-right` 来补偿滚动条消失导致的内容位移

这种机制在某些情况下会导致页面内容突然向右偏移、布局闪动或错位。

## 完整解决方案

我们采用了多层次的解决方案来彻底解决这个问题：

### 1. 核心方案：JavaScript 禁用滚动条补偿

通过 JavaScript 重写 Ant Design 的滚动条检测机制：

```typescript
// 重写 getScrollBarSize 方法，返回 0 来禁用滚动条补偿
(window as any).getScrollBarSize = () => 0

// 监听 DOM 变化，确保 body 的 padding-right 不被修改
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
      const target = mutation.target as HTMLElement
      if (target === document.body) {
        const style = target.style.paddingRight
        if (style && style !== '0px' && target.style.overflow === 'hidden') {
          target.style.paddingRight = '0px'
        }
      }
    }
  })
})
```

### 2. CSS 辅助方案：预留滚动条空间

```css
/* 核心解决方案：禁用 Ant Design 的滚动条补偿 */
body.ant-scrolling-effect {
  padding-right: 0 !important;
}

/* 为支持 scrollbar-gutter 的浏览器提供更好的体验 */
@supports (scrollbar-gutter: stable) {
  body {
    scrollbar-gutter: stable;
  }
}
```

### 3. ConfigProvider 配置

在应用的根组件中配置 ConfigProvider：

```tsx
<AntdConfigProvider
  theme={theme}
  wave={{ disabled: true }}
  getPopupContainer={(node) => {
    if (node) {
      return node.parentNode as HTMLElement
    }
    return document.body
  }}
>
  <ModalOptimizer />
  {/* 其他组件 */}
</AntdConfigProvider>
```

### 4. 自动初始化组件

使用 `ModalOptimizer` 组件自动初始化优化：

```tsx
// ModalOptimizer.tsx
'use client'
import { useEffect } from 'react'
import { useModalOptimizationInit } from '@ninebot/core'

export default function ModalOptimizer() {
  useEffect(() => {
    const cleanup = useModalOptimizationInit()
    return cleanup
  }, [])

  return null
}
```

## 使用方法

### 1. 自动生效

优化已经在项目中全局部署，所有弹框组件都会自动受益：

- Web 应用：在 `apps/web/src/app/[locale]/layout.tsx` 中已添加 `ModalOptimizer`
- H5 应用：在 `apps/h5/src/app/[locale]/layout.tsx` 中已添加 `ModalOptimizer`

### 2. 手动调试

```typescript
import { debugScrollbarInfo } from '@ninebot/core'

// 在浏览器控制台运行，查看当前页面滚动条信息
debugScrollbarInfo()
```

### 3. 高级配置（可选）

```typescript
import { useModalOptimization } from '@ninebot/core'

function MyComponent() {
  const { isOptimized, needsManualFix } = useModalOptimization({
    disableScrollbarCompensation: true
  })

  console.log('弹框优化状态:', { isOptimized, needsManualFix })

  return <Modal>{/* 内容 */}</Modal>
}
```

## 配置选项

### ModalOptimizationConfig

```typescript
interface ModalOptimizationConfig {
  /** 是否禁用 body 滚动锁定 */
  disableBodyScrollLock?: boolean
  /** 是否禁用滚动条补偿 */
  disableScrollbarCompensation?: boolean
  /** 自定义滚动条补偿值 */
  customScrollbarWidth?: number
}
```

## 测试方法

1. **创建有滚动条的页面**：确保页面内容超出视口高度
2. **打开弹框**：观察页面是否有横向位移
3. **关闭弹框**：观察页面是否恢复原位
4. **多次切换**：确保没有累积偏移

## 调试工具

使用 `debugScrollbarInfo()` 函数可以在控制台查看当前页面的滚动条信息：

```javascript
// 在浏览器控制台运行
debugScrollbarInfo()
```

输出信息包括：
- 浏览器是否支持 `scrollbar-gutter`
- 当前滚动条宽度
- 页面是否有垂直滚动条
- 视口和文档宽度信息

## 最佳实践

1. **优先使用 CSS 方案**：现代浏览器推荐使用 `scrollbar-gutter: stable`
2. **保持兼容性**：为旧浏览器提供 fallback 方案
3. **测试多种场景**：不同屏幕尺寸、有无滚动条的页面
4. **监控用户反馈**：关注用户体验，及时调整优化策略

## 注意事项

1. **移动端**：移动端通常不显示滚动条，抖动问题较少
2. **自定义滚动条**：如果使用了自定义滚动条样式，可能需要额外调整
3. **嵌套弹框**：多层弹框可能需要特殊处理
4. **第三方组件**：确保第三方弹框组件也遵循相同的优化策略

## 效果验证

### 测试步骤

1. **创建测试页面**：确保页面内容超出视口高度，产生垂直滚动条
2. **打开弹框**：点击任意 Modal、Drawer 或其他弹框组件
3. **观察页面**：页面内容应该保持稳定，不会有横向位移
4. **关闭弹框**：页面应该恢复到原始状态，无累积偏移
5. **重复测试**：多次打开关闭，确保稳定性

### 优化效果

- ✅ **彻底解决抖动**：弹框打开时页面不再横向偏移
- ✅ **位置稳定**：弹框关闭时页面位置保持稳定
- ✅ **全局生效**：所有 Ant Design 弹框组件自动受益
- ✅ **性能优秀**：JavaScript 优化 + CSS 辅助，性能影响最小
- ✅ **兼容性好**：支持现代浏览器和旧版浏览器
- ✅ **维护简单**：一次配置，全局生效

## 技术原理

这套方案的核心是：

1. **拦截源头**：重写 `getScrollBarSize` 方法，让 Ant Design 认为滚动条宽度为 0
2. **监听变化**：使用 `MutationObserver` 监听 body 样式变化，强制移除 `padding-right`
3. **CSS 辅助**：使用 `scrollbar-gutter: stable` 预留滚动条空间
4. **自动初始化**：通过 `ModalOptimizer` 组件在应用启动时自动启用优化

这种多层次的解决方案确保了在各种浏览器和场景下都能有效防止弹框抖动问题。
