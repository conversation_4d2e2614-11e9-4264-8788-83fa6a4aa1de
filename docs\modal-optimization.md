# Modal 弹框抖动优化方案 - 完整解决方案

## 问题描述

Ant Design 的弹框组件（Modal、Drawer、Popconfirm 等）在显示时会：

1. 给 `<body>` 添加 `overflow: hidden` 来禁止滚动
2. 根据滚动条宽度添加 `padding-right` 来补偿滚动条消失导致的内容位移

这种机制在某些情况下会导致页面内容突然向右偏移、布局闪动或错位。

## 完整解决方案

我们采用了多层次的解决方案来彻底解决这个问题：

### 1. 主要方案：CSS `scrollbar-gutter`

我们在全局样式中添加了 `scrollbar-gutter: stable`，这是现代浏览器推荐的解决方案：

```css
body {
  /* 预留滚动条空间，防止弹框出现时页面抖动 */
  scrollbar-gutter: stable;
}
```

**优点：**
- 简单有效，一行 CSS 解决问题
- 现代浏览器原生支持
- 性能最佳，无需 JavaScript 计算

**浏览器支持：**
- Chrome 94+
- Firefox 97+
- Safari 16.4+

### 2. 兼容方案：传统 padding 补偿

对于不支持 `scrollbar-gutter` 的浏览器，我们提供了兼容方案：

```css
@supports not (scrollbar-gutter: stable) {
  body {
    padding-right: calc(100vw - 100%);
    margin-right: calc(-1 * (100vw - 100%));
  }
  
  body.ant-scrolling-effect {
    padding-right: calc(100vw - 100%) !important;
  }
}
```

### 3. 精细控制：禁用 Ant Design 的默认补偿

```css
@supports (scrollbar-gutter: stable) {
  body.ant-scrolling-effect {
    padding-right: 0 !important;
  }
}
```

## 使用工具函数

我们提供了一套工具函数来帮助开发者更好地控制弹框行为：

```typescript
import { useModalOptimization, debugScrollbarInfo } from '@ninebot/core'

// 在组件中使用
function MyComponent() {
  const { isOptimized, needsManualFix, modalProps } = useModalOptimization({
    disableScrollbarCompensation: true
  })

  return (
    <Modal {...modalProps}>
      {/* 内容 */}
    </Modal>
  )
}

// 调试滚动条信息
debugScrollbarInfo()
```

## 配置选项

### ModalOptimizationConfig

```typescript
interface ModalOptimizationConfig {
  /** 是否禁用 body 滚动锁定 */
  disableBodyScrollLock?: boolean
  /** 是否禁用滚动条补偿 */
  disableScrollbarCompensation?: boolean
  /** 自定义滚动条补偿值 */
  customScrollbarWidth?: number
}
```

## 测试方法

1. **创建有滚动条的页面**：确保页面内容超出视口高度
2. **打开弹框**：观察页面是否有横向位移
3. **关闭弹框**：观察页面是否恢复原位
4. **多次切换**：确保没有累积偏移

## 调试工具

使用 `debugScrollbarInfo()` 函数可以在控制台查看当前页面的滚动条信息：

```javascript
// 在浏览器控制台运行
debugScrollbarInfo()
```

输出信息包括：
- 浏览器是否支持 `scrollbar-gutter`
- 当前滚动条宽度
- 页面是否有垂直滚动条
- 视口和文档宽度信息

## 最佳实践

1. **优先使用 CSS 方案**：现代浏览器推荐使用 `scrollbar-gutter: stable`
2. **保持兼容性**：为旧浏览器提供 fallback 方案
3. **测试多种场景**：不同屏幕尺寸、有无滚动条的页面
4. **监控用户反馈**：关注用户体验，及时调整优化策略

## 注意事项

1. **移动端**：移动端通常不显示滚动条，抖动问题较少
2. **自定义滚动条**：如果使用了自定义滚动条样式，可能需要额外调整
3. **嵌套弹框**：多层弹框可能需要特殊处理
4. **第三方组件**：确保第三方弹框组件也遵循相同的优化策略

## 效果验证

优化后的效果：
- ✅ 弹框打开时页面不再横向偏移
- ✅ 弹框关闭时页面位置保持稳定
- ✅ 支持现代浏览器的原生优化
- ✅ 兼容旧版浏览器
- ✅ 性能影响最小

这套方案已经在项目中全局部署，所有弹框组件都会自动受益于这些优化。
