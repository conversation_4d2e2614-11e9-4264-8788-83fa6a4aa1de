import { notFound } from 'next/navigation'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { AntdRegistry } from '@ant-design/nextjs-registry'
import { miSansFamilyVariable } from '@public/fonts'
import {
  AuthProvider,
  GlobalProvider,
  LoadingProvider,
  StoreProvider,
  ToastProvider,
} from '@ninebot/core'
import { TBaseComponentProps } from '@ninebot/core/src/typings'
import { ConfigProvider as AntdConfigProvider } from 'antd'
import clsx from 'clsx'
import { getLangDir } from 'rtl-detect'

import { <PERSON><PERSON>, Header, ModalOptimizer } from '@/components'
import TabProvider from '@/components/business/marketing/TabContext'
import { i18nRouting } from '@/config'
import { TLocales } from '@/i18n/type'
import theme from '@/styles/theme/antd'

type TLocalLayoutProps = TBaseComponentProps<{
  params: { locale: TLocales }
}>

/**
 * 局部布局组件
 */
const LocalLayout = async ({ children, params }: TLocalLayoutProps) => {
  const { locale } = params

  // Ensure that the incoming `locale` is valid
  if (!i18nRouting.locales.includes(locale)) {
    notFound()
  }

  // Detect the language direction based on the locale
  const direction = getLangDir(locale)

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()

  return (
    <html lang={locale} dir={direction}>
      <body
        className={clsx(
          miSansFamilyVariable,
          'h-full scroll-smooth font-miSansMedium380 text-base text-black',
        )}>
        <div id="app" className="flex min-h-screen flex-col">
          <NextIntlClientProvider messages={messages}>
            <StoreProvider>
              <GlobalProvider>
                <LoadingProvider>
                  <ToastProvider
                    position="top"
                    contentClassName="gap-[8px] px-[16px] py-[12px]"
                    textClassName="text-[16px] leading-[1.4]"
                    iconWidth={22}
                    iconHeight={22}>
                    <AuthProvider>
                      <AntdRegistry>
                        <AntdConfigProvider
                          theme={theme}
                          wave={{ disabled: true }}
                          getPopupContainer={(node) => {
                            // 确保弹框渲染到正确的容器中
                            if (node) {
                              return node.parentNode as HTMLElement
                            }
                            return document.body
                          }}>
                          <ModalOptimizer />
                          <TabProvider>
                            <Header />
                            {children}
                            <Footer />
                          </TabProvider>
                        </AntdConfigProvider>
                      </AntdRegistry>
                    </AuthProvider>
                  </ToastProvider>
                </LoadingProvider>
              </GlobalProvider>
            </StoreProvider>
          </NextIntlClientProvider>
        </div>
      </body>
    </html>
  )
}

export default LocalLayout
